using FluentAssertions;
using SmaTrendFollower.Tests.Core.TestHelpers;
using NSubstitute;
using RichardSzalay.MockHttp;
using System.Net;

namespace SmaTrendFollower.Tests.Core;

/// <summary>
/// Example tests demonstrating the new test infrastructure
/// </summary>
public class ExampleTests : IDisposable
{
    private readonly RedisTestFixture _redisFixture;

    public ExampleTests()
    {
        _redisFixture = new RedisTestFixture();
    }

    [Fact]
    public async Task Redis_Helper_Should_Work()
    {
        // Arrange
        var database = _redisFixture.Database;
        const string key = "test:example";
        const string value = "example-value";

        // Act
        await database.StringSetAsync(key, value);
        var result = await database.StringGetAsync(key);

        // Assert
        result.Should().Be(value);
    }

    [Fact]
    public async Task Redis_Helper_Should_Clear_Database()
    {
        // Arrange
        var database = _redisFixture.Database;
        await database.StringSetAsync("test:key1", "value1");
        await database.StringSetAsync("test:key2", "value2");

        // Act
        await _redisFixture.ClearAsync();
        var key1Exists = await database.KeyExistsAsync("test:key1");
        var key2Exists = await database.KeyExistsAsync("test:key2");

        // Assert
        key1Exists.Should().BeFalse();
        key2Exists.Should().BeFalse();
    }

    [Fact]
    public void MockPolygon_Should_Mock_Stock_Bars()
    {
        // Arrange
        using var mockPolygon = new MockPolygon();
        var symbol = "AAPL";
        var bars = new[]
        {
            MockPolygon.CreateSampleBar(1640995200000, 150.0m, 155.0m, 149.0m, 154.0m, 1000000)
        };

        // Act
        mockPolygon.MockStockBars(symbol, bars);
        
        // Assert - This would be used in actual service tests
        mockPolygon.HttpClient.Should().NotBeNull();
    }

    [Fact]
    public void NSubstitute_Should_Work()
    {
        // Arrange
        var mockService = Substitute.For<IExampleService>();
        mockService.GetValue().Returns("mocked-value");

        // Act
        var result = mockService.GetValue();

        // Assert
        result.Should().Be("mocked-value");
    }

    [Fact]
    public void FluentAssertions_Should_Work()
    {
        // Arrange
        var list = new List<string> { "item1", "item2", "item3" };

        // Act & Assert
        list.Should().HaveCount(3);
        list.Should().Contain("item2");
        list.Should().NotContain("item4");
    }

    public void Dispose()
    {
        _redisFixture?.Dispose();
    }
}

// Example interface for testing NSubstitute
public interface IExampleService
{
    string GetValue();
}
