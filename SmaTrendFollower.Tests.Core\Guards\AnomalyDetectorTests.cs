using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Extensions.Logging.Abstractions;
using NSubstitute;
using SmaTrendFollower.Services;
using StackExchange.Redis;
using Xunit;

namespace SmaTrendFollower.Tests.Core.Guards;

public class AnomalyDetectorTests
{
    [Fact]
    public void AnomalyDetectorService_CanBeCreated()
    {
        // Arrange
        var mockMux = Substitute.For<IConnectionMultiplexer>();
        var mockDb = Substitute.For<IDatabase>();
        mockMux.GetDatabase(Arg.Any<int>(), Arg.Any<object>()).Returns(mockDb);

        // Act & Assert - should create without throwing
        var act = () => new AnomalyDetectorService(mockMux, NullLogger<AnomalyDetectorService>.Instance);
        act.Should().NotThrow();
    }

    [Fact]
    public async Task PriceSpike_HaltsSymbol()
    {
        // Arrange - establish baseline prices
        for (int i = 0; i < 200; i++)
            _anom.OnTrade("XYZ", 100m, System.DateTime.UtcNow.AddMilliseconds(i));

        // Act - introduce price spike (50% jump)
        _anom.OnTrade("XYZ", 150m, System.DateTime.UtcNow);

        // Wait for async halt operation to complete
        await Task.Delay(100);

        // Assert - halt key should be set
        _mux.GetDatabase().KeyExists("halt:XYZ").Should().BeTrue();
    }

    [Fact]
    public async Task SpreadAnomaly_HaltsSymbol()
    {
        // Arrange - establish baseline spreads
        for (int i = 0; i < 200; i++)
            _anom.OnQuote("ABC", 100m, 100.10m, System.DateTime.UtcNow.AddMilliseconds(i));

        // Act - introduce spread anomaly (very wide spread)
        _anom.OnQuote("ABC", 100m, 105m, System.DateTime.UtcNow);

        // Wait for async halt operation to complete
        await Task.Delay(100);

        // Assert - halt key should be set
        _mux.GetDatabase().KeyExists("halt:ABC").Should().BeTrue();
    }

    [Fact]
    public void OnTrade_DoesNotHalt_WithInsufficientData()
    {
        // Arrange & Act - only a few trades, not enough for statistical analysis
        for (int i = 0; i < 10; i++)
            _anom.OnTrade("DEF", 100m + i * 10, System.DateTime.UtcNow.AddMilliseconds(i));

        // Assert - should not halt with insufficient data
        _mux.GetDatabase().KeyExists("halt:DEF").Should().BeFalse();
    }

    [Fact]
    public void OnQuote_HandlesInvalidInputs_Gracefully()
    {
        // Act & Assert - should not throw exceptions
        _anom.OnQuote("", 100m, 100.10m, System.DateTime.UtcNow);     // empty symbol
        _anom.OnQuote("GHI", 0m, 100.10m, System.DateTime.UtcNow);    // zero bid
        _anom.OnQuote("GHI", 100m, 0m, System.DateTime.UtcNow);       // zero ask
        _anom.OnQuote("GHI", 100m, 99m, System.DateTime.UtcNow);      // ask < bid

        // Should not create any halt keys for invalid inputs
        _mux.GetDatabase().KeyExists("halt:").Should().BeFalse();
        _mux.GetDatabase().KeyExists("halt:GHI").Should().BeFalse();
    }

    [Fact]
    public void OnTrade_HandlesInvalidInputs_Gracefully()
    {
        // Act & Assert - should not throw exceptions
        _anom.OnTrade("", 100m, System.DateTime.UtcNow);              // empty symbol
        _anom.OnTrade("JKL", 0m, System.DateTime.UtcNow);             // zero price
        _anom.OnTrade("JKL", -100m, System.DateTime.UtcNow);          // negative price

        // Should not create any halt keys for invalid inputs
        _mux.GetDatabase().KeyExists("halt:").Should().BeFalse();
        _mux.GetDatabase().KeyExists("halt:JKL").Should().BeFalse();
    }

    public void Dispose()
    {
        _anom?.Dispose();
        _mux?.Dispose();
    }
}
