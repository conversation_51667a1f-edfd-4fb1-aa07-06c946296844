using System;
using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Extensions.Logging.Abstractions;
using NSubstitute;
using Quartz;
using SmaTrendFollower.Services;
using SmaTrendFollower.Tests.Core.TestHelpers;
using StackExchange.Redis;
using Xunit;

namespace SmaTrendFollower.Tests.Core.Infrastructure;

public class RedisTTLTests
{
    [Fact]
    public async Task RedisCleanupJob_DoesNotThrow()
    {
        // Arrange - Create mock Redis service that simulates successful operations
        var mockRedisService = Substitute.For<IOptimizedRedisConnectionService>();
        var mockDatabase = Substitute.For<IDatabase>();

        // Setup mock to return a database
        mockRedisService.GetDatabaseAsync().Returns(mockDatabase);

        var job = new RedisCleanupService(
            mockRedisService,
            NullLogger<RedisCleanupService>.Instance);

        // Create a mock job execution context
        var mockContext = Substitute.For<IJobExecutionContext>();
        mockContext.CancellationToken.Returns(System.Threading.CancellationToken.None);

        // Act & Assert - should not throw even if Redis operations fail
        var act = async () => await job.Execute(mockContext);
        await act.Should().NotThrowAsync();
    }

    [Fact]
    public async Task RedisCleanupJob_HandlesServiceInitialization()
    {
        // Arrange - Test that the service can be created and initialized properly
        var mockRedisService = Substitute.For<IOptimizedRedisConnectionService>();
        var mockDatabase = Substitute.For<IDatabase>();

        mockRedisService.GetDatabaseAsync().Returns(mockDatabase);

        // Act & Assert - service should initialize without throwing
        var act = () => new RedisCleanupService(
            mockRedisService,
            NullLogger<RedisCleanupService>.Instance);

        act.Should().NotThrow();

        var job = act();
        job.Should().NotBeNull();
    }

    [Fact]
    public void RedisCleanupJob_RequiresValidDependencies()
    {
        // Arrange & Act & Assert - should throw when given null dependencies
        var act1 = () => new RedisCleanupService(
            null!,
            NullLogger<RedisCleanupService>.Instance);

        var act2 = () => new RedisCleanupService(
            Substitute.For<IOptimizedRedisConnectionService>(),
            null!);

        act1.Should().Throw<ArgumentNullException>();
        act2.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public async Task RedisCleanupJob_HandlesNullContext()
    {
        // Arrange
        var mockRedisService = Substitute.For<IOptimizedRedisConnectionService>();
        var mockDatabase = Substitute.For<IDatabase>();
        mockRedisService.GetDatabaseAsync().Returns(mockDatabase);

        var job = new RedisCleanupService(
            mockRedisService,
            NullLogger<RedisCleanupService>.Instance);

        // Act & Assert - should handle null context gracefully
        var act = async () => await job.Execute(null!);
        await act.Should().NotThrowAsync();
    }
}
