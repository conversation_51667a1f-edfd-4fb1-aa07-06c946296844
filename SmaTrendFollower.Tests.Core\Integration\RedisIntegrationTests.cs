using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Interfaces;
using SmaTrendFollower.Services;
using SmaTrendFollower.Tests.Core.TestHelpers;
using StackExchange.Redis;
using Xunit;

namespace SmaTrendFollower.Tests.Core.Integration;

/// <summary>
/// Comprehensive Redis integration tests to verify all Redis-dependent production services work correctly
/// </summary>
public class RedisIntegrationTests : IDisposable
{
    private readonly ConnectionMultiplexer _connection;
    private readonly IServiceProvider _serviceProvider;
    private readonly IDatabase _database;

    public RedisIntegrationTests()
    {
        _connection = InMemoryRedis.Create();
        _database = InMemoryRedis.GetDatabase();

        // Setup service collection with Redis-dependent services
        var services = new ServiceCollection();
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["REDIS_URL"] = "*************:6379",
                ["REDIS_DATABASE"] = "0"
            })
            .Build();

        services.AddSingleton<IConfiguration>(configuration);
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));
        services.AddSingleton(_connection);
        services.AddSingleton<IDatabase>(_database);

        // Add Redis-dependent services
        services.AddSingleton<IOptimizedRedisConnectionService, OptimizedRedisConnectionService>();
        services.AddSingleton<ILiveStateStore, LiveStateStore>();

        _serviceProvider = services.BuildServiceProvider();
    }

    [Fact]
    public async Task Redis_Connection_Should_Be_Healthy()
    {
        // Arrange & Act
        var isConnected = _connection.IsConnected;
        var pingResult = await _database.PingAsync();

        // Assert
        isConnected.Should().BeTrue("Redis connection should be established");
        pingResult.Should().BeLessThan(TimeSpan.FromSeconds(1), "Redis ping should be fast");
    }

    [Fact]
    public async Task LiveStateStore_Should_Store_And_Retrieve_Market_State()
    {
        // Arrange
        var liveStateStore = _serviceProvider.GetRequiredService<ILiveStateStore>();
        var testKey = "test_market_state";
        var testValue = DateTime.UtcNow.ToString("O");

        // Act
        await liveStateStore.SetMarketStateAsync(testKey, testValue, TimeSpan.FromMinutes(5));
        var retrievedValue = await liveStateStore.GetMarketStateAsync<string>(testKey);

        // Assert
        retrievedValue.Should().Be(testValue);
    }

    [Fact]
    public async Task Redis_Should_Handle_Signal_Flags()
    {
        // Arrange
        var symbol = "AAPL";
        var today = DateTime.UtcNow.Date;
        var signalKey = $"signal:{symbol}:{today:yyyy-MM-dd}";

        // Act
        await _database.StringSetAsync(signalKey, "BUY", TimeSpan.FromHours(24));
        var retrievedSignal = await _database.StringGetAsync(signalKey);

        // Assert
        retrievedSignal.Should().Be("BUY");
    }

    [Fact]
    public async Task Redis_Should_Handle_Trailing_Stops()
    {
        // Arrange
        var symbol = "MSFT";
        var stopLevel = 150.25m;
        var stopKey = $"stop:{symbol}";

        // Act
        await _database.StringSetAsync(stopKey, stopLevel.ToString(), TimeSpan.FromDays(7));
        var retrievedStop = await _database.StringGetAsync(stopKey);

        // Assert
        decimal.Parse(retrievedStop.ToString()).Should().Be(stopLevel);
    }

    [Fact]
    public async Task Redis_Should_Handle_Market_Regime_Caching()
    {
        // Arrange
        var regimeKey = "market:regime:spy";
        var regimeData = new
        {
            Regime = "TrendingUp",
            Confidence = 0.85m,
            DetectedAt = DateTime.UtcNow,
            Metadata = "Test regime data"
        };

        // Act
        await _database.StringSetAsync(regimeKey, System.Text.Json.JsonSerializer.Serialize(regimeData), TimeSpan.FromHours(24));
        var retrievedData = await _database.StringGetAsync(regimeKey);

        // Assert
        retrievedData.Should().NotBeNull();
        retrievedData.ToString().Should().Contain("TrendingUp");
    }

    [Fact]
    public async Task Redis_Should_Handle_Universe_Caching()
    {
        // Arrange
        var universeKey = "universe:candidates";
        var symbols = new[] { "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA" };
        var symbolsString = string.Join(",", symbols);

        // Act
        await _database.StringSetAsync(universeKey, symbolsString, TimeSpan.FromDays(8));
        var retrievedSymbols = await _database.StringGetAsync(universeKey);

        // Assert
        retrievedSymbols.Should().Be(symbolsString);
        retrievedSymbols.ToString().Split(',').Should().HaveCount(5);
    }

    [Fact]
    public async Task Redis_Should_Handle_VIX_Data_Caching()
    {
        // Arrange
        var vixKey = "vix:current";
        var vixData = new
        {
            Value = 16.75m,
            Timestamp = DateTime.UtcNow,
            Source = "Polygon"
        };

        // Act
        await _database.StringSetAsync(vixKey, System.Text.Json.JsonSerializer.Serialize(vixData), TimeSpan.FromMinutes(15));
        var retrievedVix = await _database.StringGetAsync(vixKey);

        // Assert
        retrievedVix.Should().NotBeNull();
        retrievedVix.ToString().Should().Contain("16.75");
    }

    [Fact]
    public async Task Redis_Should_Handle_Batch_Operations()
    {
        // Arrange
        var batch = _database.CreateBatch();
        var keys = new[] { "batch:test1", "batch:test2", "batch:test3" };
        var values = new[] { "value1", "value2", "value3" };

        // Act
        for (int i = 0; i < keys.Length; i++)
        {
            _ = batch.StringSetAsync(keys[i], values[i], TimeSpan.FromMinutes(5));
        }
        batch.Execute();

        // Verify all keys were set
        var redisKeys = keys.Select(k => (RedisKey)k).ToArray();
        var retrievedValues = await _database.StringGetAsync(redisKeys);

        // Assert
        retrievedValues.Should().HaveCount(3);
        for (int i = 0; i < retrievedValues.Length; i++)
        {
            retrievedValues[i].Should().Be(values[i]);
        }
    }

    [Fact]
    public async Task OptimizedRedisConnectionService_Should_Work()
    {
        // Arrange
        var redisService = _serviceProvider.GetRequiredService<IOptimizedRedisConnectionService>();

        // Act
        var database = await redisService.GetDatabaseAsync();
        var healthStatus = await redisService.GetHealthStatusAsync();

        // Assert
        database.Should().NotBeNull();
        healthStatus.IsHealthy.Should().BeTrue();
    }

    [Fact]
    public async Task Redis_Should_Handle_TTL_Correctly()
    {
        // Arrange
        var testKey = "ttl:test";
        var testValue = "test_value";
        var ttl = TimeSpan.FromSeconds(2);

        // Act
        await _database.StringSetAsync(testKey, testValue, ttl);
        var initialValue = await _database.StringGetAsync(testKey);
        
        // Wait for expiration
        await Task.Delay(TimeSpan.FromSeconds(3));
        var expiredValue = await _database.StringGetAsync(testKey);

        // Assert
        initialValue.Should().Be(testValue);
        expiredValue.Should().BeNull();
    }

    [Fact]
    public async Task Redis_Should_Handle_Hash_Operations()
    {
        // Arrange
        var hashKey = "position:AAPL";
        var positionData = new Dictionary<string, string>
        {
            ["quantity"] = "100",
            ["entry_price"] = "150.25",
            ["stop_loss"] = "145.00",
            ["timestamp"] = DateTime.UtcNow.ToString("O")
        };

        // Act
        await _database.HashSetAsync(hashKey, positionData.Select(kvp => new HashEntry(kvp.Key, kvp.Value)).ToArray());
        var retrievedData = await _database.HashGetAllAsync(hashKey);

        // Assert
        retrievedData.Should().HaveCount(4);
        retrievedData.ToDictionary(he => he.Name, he => he.Value)["quantity"].Should().Be("100");
        retrievedData.ToDictionary(he => he.Name, he => he.Value)["entry_price"].Should().Be("150.25");
    }

    public void Dispose()
    {
        _serviceProvider?.GetService<IOptimizedRedisConnectionService>()?.Dispose();
        _connection?.Dispose();
    }
}
