using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.ML;
using NSubstitute;
using StackExchange.Redis;
using SmaTrendFollower.Services;
using SmaTrendFollower.Tests.Core.TestHelpers;
using Xunit;

namespace SmaTrendFollower.Tests.Core.Ml;

public class RegimeClassifierReloadTests
{
    [Fact]
    public async Task ReloadsModel_WhenRedisVersionChanges()
    {
        // Arrange
        var mux = InMemoryRedis.Create();
        var db = mux.GetDatabase();
        await db.StringSetAsync("model:regime:version", 1);

        // Create mock dependencies
        var mockMarketDataService = Substitute.For<IMarketDataService>();
        var mockRedisService = Substitute.For<IOptimizedRedisConnectionService>();
        var mockBreadthService = Substitute.For<IBreadthService>();

        // Setup mock Redis service to return our test database
        mockRedisService.GetDatabaseAsync().Returns(db);

        var svc = new RegimeClassifierService(
            mockMarketDataService,
            mockRedisService,
            mockBreadthService,
            NullLogger<RegimeClassifierService>.Instance);

        // Act - initial detection (should load model version 1)
        await svc.DetectTodayAsync();

        // Change version in Redis
        await db.StringSetAsync("model:regime:version", 2);
        
        // Act - detect again (should reload with version 2)
        await svc.DetectTodayAsync();

        // Assert - should have detected version change
        var currentVersion = await svc.GetModelVersionAsync();
        currentVersion.Should().Be(2);
    }

    [Fact]
    public async Task GetModelVersionAsync_ReturnsCorrectVersion()
    {
        // Arrange
        var mux = InMemoryRedis.Create();
        var db = mux.GetDatabase();
        await db.StringSetAsync("model:regime:version", 42);

        var mockMarketDataService = Substitute.For<IMarketDataService>();
        var mockRedisService = Substitute.For<IOptimizedRedisConnectionService>();
        var mockBreadthService = Substitute.For<IBreadthService>();

        mockRedisService.GetDatabaseAsync().Returns(db);

        var svc = new RegimeClassifierService(
            mockMarketDataService,
            mockRedisService,
            mockBreadthService,
            NullLogger<RegimeClassifierService>.Instance);

        // Act
        var version = await svc.GetModelVersionAsync();

        // Assert
        version.Should().Be(42);
    }

    [Fact]
    public async Task DetectTodayAsync_HandlesNoRedisVersion_Gracefully()
    {
        // Arrange
        var mux = InMemoryRedis.Create();
        var db = mux.GetDatabase();
        // Don't set any version in Redis

        var mockMarketDataService = Substitute.For<IMarketDataService>();
        var mockRedisService = Substitute.For<IOptimizedRedisConnectionService>();
        var mockBreadthService = Substitute.For<IBreadthService>();

        mockRedisService.GetDatabaseAsync().Returns(db);

        var svc = new RegimeClassifierService(
            mockMarketDataService,
            mockRedisService,
            mockBreadthService,
            NullLogger<RegimeClassifierService>.Instance);

        // Act & Assert - should not throw even without Redis version
        var act = async () => await svc.DetectTodayAsync();
        await act.Should().NotThrowAsync();
    }

    [Fact]
    public async Task ReloadModelAsync_DoesNotThrow()
    {
        // Arrange
        var mux = InMemoryRedis.Create();
        var db = mux.GetDatabase();

        var mockMarketDataService = Substitute.For<IMarketDataService>();
        var mockRedisService = Substitute.For<IOptimizedRedisConnectionService>();
        var mockBreadthService = Substitute.For<IBreadthService>();

        mockRedisService.GetDatabaseAsync().Returns(db);

        var svc = new RegimeClassifierService(
            mockMarketDataService,
            mockRedisService,
            mockBreadthService,
            NullLogger<RegimeClassifierService>.Instance);

        // Act & Assert - should handle model reload gracefully even if no model file exists
        var act = async () => await svc.ReloadModelAsync();
        await act.Should().NotThrowAsync();
    }
}
