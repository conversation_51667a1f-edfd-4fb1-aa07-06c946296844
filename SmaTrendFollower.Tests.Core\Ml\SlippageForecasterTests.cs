using System.Threading.Tasks;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.ML;
using StackExchange.Redis;
using SmaTrendFollower.MachineLearning.Prediction;
using SmaTrendFollower.Models;
using SmaTrendFollower.Tests.Core.TestHelpers;
using Xunit;

namespace SmaTrendFollower.Tests.Core.Ml;

public class SlippageForecasterTests
{
    private readonly SlippageForecasterService _fore;
    private readonly IDatabase _db;

    public SlippageForecasterTests()
    {
        _db = InMemoryRedis.Create().GetDatabase();
        
        // Set up mock configuration
        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["SlippageForecaster:ModelPath"] = "Model/slippage_model.zip"
            })
            .Build();

        // Assume a dummy model.zip exists; otherwise forecaster falls back & returns default
        _db.StringSet("model:slip:version", 1);
        
        // Create service with mock Redis service (using null for simplicity in unit tests)
        _fore = new SlippageForecasterService(
            NullLogger<SlippageForecasterService>.Instance,
            null, // IOptimizedRedisConnectionService - using null for unit tests
            configuration);
    }

    [Fact]
    public void PredictBps_ReturnsFloat()
    {
        // Arrange
        var f = new SlippageSignalFeatures(
            Symbol: "AAPL",
            RankProb: 0.8f, 
            ATR_Pct: 2.0f, 
            VolumePct10d: 100f, 
            Regime: 0.5f, // MarketRegime.Sideways equivalent
            Side: SmaOrderSide.Buy);
            
        var q = new QuoteContext(
            MidPrice: 150m,
            SpreadPct: 0.05f, 
            TimestampUtc: System.DateTime.UtcNow);

        // Act
        float bps = _fore.PredictBps(f, q);

        // Assert - should return reasonable slippage range
        bps.Should().BeInRange(-50f, 50f); // sanity check for reasonable slippage
    }

    [Fact]
    public void PredictBps_HandlesNullInputs_Gracefully()
    {
        // Arrange
        var f = new SlippageSignalFeatures(
            Symbol: null,
            RankProb: 0.8f, 
            ATR_Pct: 2.0f, 
            VolumePct10d: 100f, 
            Regime: 0.5f,
            Side: SmaOrderSide.Buy);
            
        var q = new QuoteContext(
            MidPrice: 150m,
            SpreadPct: 0.05f, 
            TimestampUtc: System.DateTime.UtcNow);

        // Act & Assert - should not throw
        var result = _fore.PredictBps(f, q);
        result.Should().BeInRange(-100f, 100f); // Should return some reasonable default
    }

    [Fact]
    public void IsReady_ReturnsFalse_WhenNoModelLoaded()
    {
        // Act & Assert - without a trained model, service should not be ready
        // Note: In real scenarios, this might be true if model loads successfully
        // For unit tests without actual model file, it should be false or return default values
        var isReady = _fore.IsReady;
        
        // The service might still function with fallback values even when not "ready"
        isReady.Should().BeFalse();
    }

    [Fact]
    public void ModelVersion_ReturnsZero_Initially()
    {
        // Act & Assert
        var version = _fore.ModelVersion;
        version.Should().BeGreaterOrEqualTo(0);
    }

    [Fact]
    public async Task ReloadModelAsync_DoesNotThrow()
    {
        // Act & Assert - should handle model reload gracefully even if no model exists
        var act = async () => await _fore.ReloadModelAsync();
        await act.Should().NotThrowAsync();
    }
}
